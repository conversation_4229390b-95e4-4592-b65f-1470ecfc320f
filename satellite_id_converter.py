#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卫星ID转换工具
将三位数卫星ID（111-198）转换为连续编号（0-71）

卫星ID规律：
- 第一位：轨道面编号（1-9）
- 后两位：轨道面内卫星编号（1-8）
- 总共9个轨道面，每个轨道面8颗卫星，共72颗卫星
"""

import pandas as pd
import sys
from typing import Dict, List

def create_satellite_id_mapping() -> Dict[int, int]:
    """
    创建卫星ID映射表
    
    Returns:
        Dict[int, int]: 原始ID到新ID的映射字典
    """
    mapping = {}
    new_id = 0
    
    # 遍历9个轨道面
    for orbit_plane in range(1, 10):  # 1-9
        # 每个轨道面8颗卫星
        for satellite_in_plane in range(1, 9):  # 1-8
            original_id = orbit_plane * 100 + 10 + satellite_in_plane
            mapping[original_id] = new_id
            new_id += 1
    
    return mapping

def reverse_mapping(mapping: Dict[int, int]) -> Dict[int, int]:
    """
    创建反向映射（新ID到原始ID）
    
    Args:
        mapping: 原始映射字典
        
    Returns:
        Dict[int, int]: 新ID到原始ID的映射字典
    """
    return {v: k for k, v in mapping.items()}

def convert_satellite_id(original_id: int, mapping: Dict[int, int]) -> int:
    """
    转换单个卫星ID
    
    Args:
        original_id: 原始三位数ID
        mapping: ID映射字典
        
    Returns:
        int: 新的连续编号ID（0-71）
    """
    if original_id not in mapping:
        raise ValueError(f"无效的卫星ID: {original_id}")
    return mapping[original_id]

def convert_csv_file(input_file: str, output_file: str, mapping: Dict[int, int]) -> None:
    """
    转换CSV文件中的卫星ID
    
    Args:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
        mapping: ID映射字典
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        
        # 检查是否有卫星ID列
        if '卫星ID' not in df.columns:
            raise ValueError("CSV文件中未找到'卫星ID'列")
        
        # 转换卫星ID
        df['卫星ID'] = df['卫星ID'].map(mapping)
        
        # 检查是否有未映射的ID
        if df['卫星ID'].isna().any():
            unmapped_ids = df[df['卫星ID'].isna()].index.tolist()
            print(f"警告：发现未映射的卫星ID，行号：{unmapped_ids}")
        
        # 保存转换后的文件
        df.to_csv(output_file, index=False)
        print(f"转换完成！输出文件：{output_file}")
        print(f"总共处理了 {len(df)} 行数据")
        
    except Exception as e:
        print(f"转换过程中出现错误：{e}")
        sys.exit(1)

def print_mapping_table(mapping: Dict[int, int]) -> None:
    """
    打印映射表
    
    Args:
        mapping: ID映射字典
    """
    print("卫星ID映射表：")
    print("原始ID -> 新ID")
    print("-" * 20)
    
    for orbit_plane in range(1, 10):
        print(f"轨道面 {orbit_plane}:")
        for satellite_in_plane in range(1, 9):
            original_id = orbit_plane * 100 + 10 + satellite_in_plane
            new_id = mapping[original_id]
            print(f"  {original_id} -> {new_id}")
        print()

def main():
    """主函数"""
    # 创建映射表
    mapping = create_satellite_id_mapping()
    
    # 打印映射表
    print_mapping_table(mapping)
    
    # 如果提供了命令行参数，则转换文件
    if len(sys.argv) >= 2:
        input_file = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) >= 3 else input_file.replace('.csv', '_converted.csv')
        
        print(f"开始转换文件：{input_file}")
        convert_csv_file(input_file, output_file, mapping)
    else:
        print("使用方法：")
        print("python satellite_id_converter.py <输入文件> [输出文件]")
        print("例如：python satellite_id_converter.py satellite_data_72_0.csv satellite_data_converted.csv")

if __name__ == "__main__":
    main()
