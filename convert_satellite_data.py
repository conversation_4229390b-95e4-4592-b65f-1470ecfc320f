#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版卫星ID转换脚本
直接转换你的卫星数据文件
"""

import pandas as pd

def convert_satellite_data():
    """转换卫星数据文件"""
    
    # 创建映射字典
    mapping = {}
    new_id = 0
    
    # 生成映射表：111-118 -> 0-7, 121-128 -> 8-15, ..., 191-198 -> 64-71
    for orbit_plane in range(1, 10):  # 轨道面1-9
        for satellite_in_plane in range(1, 9):  # 每个轨道面的卫星1-8
            original_id = orbit_plane * 100 + 10 + satellite_in_plane
            mapping[original_id] = new_id
            new_id += 1
    
    # 打印映射表供参考
    print("卫星ID映射表：")
    print("原始ID -> 新ID")
    print("-" * 20)
    for orbit_plane in range(1, 10):
        orbit_ids = []
        for satellite_in_plane in range(1, 9):
            original_id = orbit_plane * 100 + 10 + satellite_in_plane
            new_id = mapping[original_id]
            orbit_ids.append(f"{original_id}->{new_id}")
        print(f"轨道面{orbit_plane}: {', '.join(orbit_ids)}")
    print()
    
    # 读取原始数据文件
    try:
        df = pd.read_csv('satellite_data_72_0.csv')
        print(f"读取到 {len(df)} 行数据")
        
        # 转换卫星ID
        df['卫星ID'] = df['卫星ID'].map(mapping)
        
        # 保存转换后的文件
        output_file = 'satellite_data_converted.csv'
        df.to_csv(output_file, index=False)
        
        print(f"转换完成！")
        print(f"输出文件：{output_file}")
        
        # 显示转换后的前几行数据
        print("\n转换后的前10行数据：")
        print(df.head(10).to_string(index=False))
        
        # 统计信息
        unique_satellites = df['卫星ID'].unique()
        print(f"\n统计信息：")
        print(f"转换后的卫星ID范围：{min(unique_satellites)} - {max(unique_satellites)}")
        print(f"卫星总数：{len(unique_satellites)}")
        
    except FileNotFoundError:
        print("错误：找不到文件 'satellite_data_72_0.csv'")
        print("请确保文件在当前目录下")
    except Exception as e:
        print(f"转换过程中出现错误：{e}")

if __name__ == "__main__":
    convert_satellite_data()
